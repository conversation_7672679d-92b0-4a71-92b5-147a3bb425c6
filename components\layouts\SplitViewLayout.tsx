import React, { ReactNode } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { List, MapPin, X } from "lucide-react";
import Footer from "../footer/footer";

interface SplitViewLayoutProps {
  isMapView: boolean;
  onViewChange: (showMap: boolean) => void;
  listContent: ReactNode;
  mapContent: ReactNode;
  listCount: number;
}

const SplitViewLayout: React.FC<SplitViewLayoutProps> = ({
  isMapView,
  onViewChange,
  listContent,
  mapContent,
  listCount,
}) => {
  // For mobile view (stacked)
  if (isMapView) {
    return (
      <div className="relative h-full w-full">
        {/* Full map view */}
        <div className="h-full w-full ">{mapContent}</div>

        {/* Toggle button */}
        {/* <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-40">
          <Button
            onClick={() => onViewChange(false)}
            className="flex items-center gap-2 py-3 px-5 rounded-full shadow-lg border border-gray-200 bg-white text-black hover:scale-105 transition-transform"
            variant="outline"
          >
            <List size={16} className="text-black" />
            <span className="font-medium">Show list</span>
            <span className="bg-gray-100 text-gray-800 text-xs font-medium px-2 py-1 rounded-full ml-1">
              {listCount}
            </span>
          </Button>
        </div> */}
      </div>
    );
  }

  // Desktop view (side by side)
  return (
    <div className="flex h-full w-full">
      {/* List section - left side - now scrollable */}
      <div className="w-full lg:w-[60%] h-full relative border-r px-2 pb-0 overflow-y-auto">
        {listContent}

        <Footer />
      </div>

      {/* Map section - right side, fixed position */}
      <div className="hidden lg:block w-[40%] fixed right-0 top-[120px] bottom-0 bg-gray-50 shadow-lg border-l">
        {/* Map position adjusted for HeaderWithSearch */}
        <div className="h-full w-full relative">{mapContent}</div>
      </div>
      {/* Invisible spacer div to maintain layout with fixed map */}
      <div className="hidden lg:block w-[40%]"></div>
    </div>
  );
};

export default SplitViewLayout;
